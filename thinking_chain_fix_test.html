<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>思维链修复测试</title>
    <style>
        :root {
            --bg-primary: #0a0a0a;
            --bg-secondary: #111111;
            --bg-tertiary: #1a1a1a;
            --bg-hover: #222222;
            --border-primary: #2a2a2a;
            --border-secondary: #333333;
            --text-primary: #ffffff;
            --text-secondary: #b3b3b3;
            --text-tertiary: #888888;
            --accent-primary: #3b82f6;
            --accent-hover: #2563eb;
            --accent-light: rgba(59, 130, 246, 0.1);
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--bg-secondary);
            padding: 20px;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-primary);
        }

        h1 {
            color: var(--text-primary);
            margin-bottom: 30px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            background: var(--bg-tertiary);
        }

        /* 思维链样式 - 匹配当前页面深色风格 */
        .thinking-chain {
            background: var(--bg-secondary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            margin: 1.5rem 0;
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-sm);
        }

        .thinking-chain:hover {
            border-color: var(--border-secondary);
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }

        .thinking-chain-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 1.25rem;
            cursor: pointer;
            background: var(--bg-tertiary);
            border-bottom: 1px solid var(--border-primary);
            transition: all 0.3s ease;
            user-select: none;
            min-height: 56px;
        }

        .thinking-chain-header:hover {
            background: var(--bg-hover);
        }

        .thinking-chain-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-secondary);
            flex: 1;
        }

        .thinking-chain-icon {
            width: 18px;
            height: 18px;
            transition: all 0.3s ease;
            color: var(--text-tertiary);
            flex-shrink: 0;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
        }

        .thinking-chain-header.expanded .thinking-chain-icon {
            transform: rotate(90deg);
            color: var(--accent-primary);
        }

        .thinking-chain-content {
            max-height: 0;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background: var(--bg-primary);
        }

        .thinking-chain-content.expanded {
            max-height: 500px;
            overflow-y: auto;
            border-top: 1px solid var(--border-primary);
        }

        .thinking-chain-inner {
            padding: 1.25rem 1.5rem;
            color: var(--text-primary);
            font-size: 0.9rem;
            line-height: 1.7;
            white-space: pre-wrap;
            word-wrap: break-word;
            overflow-wrap: break-word;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
        }

        .thinking-chain-inner.typing::after {
            content: '▋';
            color: var(--accent-primary);
            animation: blink 1s infinite;
            margin-left: 2px;
        }

        .thinking-chain-content::-webkit-scrollbar {
            width: 6px;
        }

        .thinking-chain-content::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        .thinking-chain-content::-webkit-scrollbar-thumb {
            background: var(--border-primary);
            border-radius: 3px;
        }

        .thinking-chain-content::-webkit-scrollbar-thumb:hover {
            background: var(--border-secondary);
        }

        .thinking-chain-badge {
            background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
            color: white;
            padding: 0.375rem 0.75rem;
            border-radius: var(--radius-md);
            font-size: 0.75rem;
            font-weight: 600;
            white-space: nowrap;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
        }

        /* 思维链动画效果 */
        .thinking-chain.thinking {
            border-color: var(--accent-primary);
            background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
            box-shadow: 0 0 0 1px var(--accent-light), var(--shadow-md);
            animation: thinking-glow 2s ease-in-out infinite;
        }

        .thinking-chain.thinking .thinking-chain-header {
            background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-hover) 100%);
        }

        .thinking-chain.thinking .thinking-chain-title {
            color: var(--accent-primary);
        }

        .thinking-chain.thinking .thinking-chain-badge {
            background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
            animation: thinking-pulse 1.5s ease-in-out infinite;
            box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
        }

        @keyframes thinking-pulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.8;
                transform: scale(1.02);
            }
        }

        @keyframes thinking-glow {
            0%, 100% {
                box-shadow: 0 0 0 1px var(--accent-light), var(--shadow-md);
            }
            50% {
                box-shadow: 0 0 0 1px var(--accent-primary), 0 0 20px rgba(59, 130, 246, 0.2), var(--shadow-lg);
            }
        }

        @keyframes blink {
            0%, 50% {
                opacity: 1;
            }
            51%, 100% {
                opacity: 0;
            }
        }

        .btn {
            background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: var(--radius-md);
            cursor: pointer;
            font-size: 0.875rem;
            margin: 5px;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-sm);
        }

        .btn:hover {
            background: linear-gradient(135deg, var(--accent-hover), #1d4ed8);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .message-content {
            background: var(--bg-primary);
            padding: 1rem 1.25rem;
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-primary);
            margin: 10px 0;
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>思维链修复测试</h1>
        
        <div class="test-section">
            <h3 style="color: var(--text-primary);">测试1：完成的思维链</h3>
            <div class="message-content">
                <p style="color: var(--text-primary);">这是一个普通的回答内容。</p>
                
                <div class="thinking-chain" data-thinking-id="test-1">
                    <div class="thinking-chain-header" onclick="toggleThinkingChain('test-1')">
                        <div class="thinking-chain-title">
                            <svg class="thinking-chain-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                            <span>思维过程</span>
                        </div>
                        <span class="thinking-chain-badge">展开查看</span>
                    </div>
                    <div class="thinking-chain-content" id="test-1-content">
                        <div class="thinking-chain-inner">这是一个思维链的内容示例。

用户问了一个关于AI的问题，我需要仔细思考如何回答。

首先，我需要分析：
1. 用户的真实需求是什么？
2. 我应该从哪个角度回答？
3. 需要提供什么样的信息？

经过思考，我认为应该这样回答...</div>
                    </div>
                </div>
                
                <p style="color: var(--text-primary);">基于我的分析，我的建议是：[具体回答内容]</p>
            </div>
        </div>

        <div class="test-section">
            <h3 style="color: var(--text-primary);">测试2：思考中的思维链</h3>
            <div class="message-content">
                <p style="color: var(--text-primary);">这是另一个回答的开始...</p>
                
                <div class="thinking-chain thinking" data-thinking-id="test-2">
                    <div class="thinking-chain-header">
                        <div class="thinking-chain-title">
                            <svg class="thinking-chain-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                            <span>思维过程</span>
                        </div>
                        <span class="thinking-chain-badge">思考中...</span>
                    </div>
                    <div class="thinking-chain-content expanded" id="test-2-content">
                        <div class="thinking-chain-inner typing" id="test-2-inner">正在思考问题的解决方案...</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3 style="color: var(--text-primary);">控制按钮</h3>
            <button class="btn" onclick="simulateThinking()">模拟思考过程</button>
            <button class="btn" onclick="completeThinking()">完成思考</button>
            <button class="btn" onclick="resetTest()">重置测试</button>
        </div>
    </div>

    <script>
        // 切换思维链展开/折叠状态
        function toggleThinkingChain(id) {
            const header = document.querySelector(`[data-thinking-id="${id}"] .thinking-chain-header`);
            const content = document.getElementById(`${id}-content`);
            const badge = header.querySelector('.thinking-chain-badge');

            if (!header || !content || !badge) return;

            const isExpanded = header.classList.contains('expanded');

            if (isExpanded) {
                // 折叠
                header.classList.remove('expanded');
                content.classList.remove('expanded');
                badge.textContent = '展开查看';
            } else {
                // 展开
                header.classList.add('expanded');
                content.classList.add('expanded');
                badge.textContent = '收起';
            }
        }

        let thinkingInterval;
        let thinkingText = '';

        function simulateThinking() {
            const thinkingChain = document.querySelector('[data-thinking-id="test-2"]');
            const badge = thinkingChain.querySelector('.thinking-chain-badge');
            const inner = document.getElementById('test-2-inner');
            
            thinkingChain.classList.add('thinking');
            badge.textContent = '思考中...';
            
            const thoughts = [
                '正在分析问题...',
                '考虑不同的解决方案...',
                '评估各种可能性...',
                '寻找最佳答案...',
                '整理思路中...'
            ];
            
            let index = 0;
            thinkingInterval = setInterval(() => {
                if (index < thoughts.length) {
                    thinkingText += thoughts[index] + '\n\n';
                    inner.textContent = thinkingText;
                    index++;
                } else {
                    clearInterval(thinkingInterval);
                }
            }, 1000);
        }

        function completeThinking() {
            clearInterval(thinkingInterval);
            const thinkingChain = document.querySelector('[data-thinking-id="test-2"]');
            const badge = thinkingChain.querySelector('.thinking-chain-badge');
            const content = document.getElementById('test-2-content');
            const header = thinkingChain.querySelector('.thinking-chain-header');
            const inner = document.getElementById('test-2-inner');
            
            thinkingChain.classList.remove('thinking');
            inner.classList.remove('typing');
            badge.textContent = '展开查看';
            
            // 默认折叠完成的思维链
            header.classList.remove('expanded');
            content.classList.remove('expanded');
        }

        function resetTest() {
            clearInterval(thinkingInterval);
            thinkingText = '';
            const inner = document.getElementById('test-2-inner');
            inner.textContent = '正在思考问题的解决方案...';
            inner.classList.add('typing');
            
            const thinkingChain = document.querySelector('[data-thinking-id="test-2"]');
            const badge = thinkingChain.querySelector('.thinking-chain-badge');
            const content = document.getElementById('test-2-content');
            const header = thinkingChain.querySelector('.thinking-chain-header');
            
            thinkingChain.classList.add('thinking');
            badge.textContent = '思考中...';
            header.classList.remove('expanded');
            content.classList.add('expanded');
        }
    </script>
</body>
</html>
