<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式输出会话隔离测试</title>
    <style>
        :root {
            --bg-primary: #0a0a0a;
            --bg-secondary: #111111;
            --bg-tertiary: #1a1a1a;
            --text-primary: #ffffff;
            --text-secondary: #b3b3b3;
            --accent-primary: #3b82f6;
            --border-primary: #2a2a2a;
            --radius-lg: 0.75rem;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            padding: 20px;
            border: 1px solid var(--border-primary);
        }

        h1 {
            color: var(--text-primary);
            margin-bottom: 30px;
            text-align: center;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: var(--bg-tertiary);
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-primary);
        }

        .chat-list {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .chat-item {
            padding: 10px 15px;
            background: var(--bg-primary);
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: var(--text-secondary);
        }

        .chat-item.active {
            background: var(--accent-primary);
            color: white;
            border-color: var(--accent-primary);
        }

        .chat-item:hover {
            border-color: var(--accent-primary);
        }

        .status-panel {
            background: var(--bg-primary);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid var(--border-primary);
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .status-label {
            color: var(--text-secondary);
        }

        .status-value {
            color: var(--text-primary);
            font-weight: 600;
        }

        .message-area {
            background: var(--bg-primary);
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            padding: 15px;
            min-height: 200px;
            margin-bottom: 20px;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
            border: 1px solid var(--border-primary);
        }

        .message.user {
            background: var(--accent-primary);
            color: white;
            margin-left: 20%;
        }

        .message.assistant {
            background: var(--bg-tertiary);
            color: var(--text-primary);
            margin-right: 20%;
        }

        .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            background: var(--accent-primary);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        .btn:disabled {
            background: #4b5563;
            cursor: not-allowed;
            transform: none;
        }

        .btn.danger {
            background: #ef4444;
        }

        .btn.danger:hover {
            background: #dc2626;
        }

        .log {
            background: var(--bg-primary);
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .log-entry {
            margin-bottom: 5px;
        }

        .log-entry.info {
            color: #3b82f6;
        }

        .log-entry.warning {
            color: #f59e0b;
        }

        .log-entry.error {
            color: #ef4444;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>流式输出会话隔离测试</h1>
        
        <div class="test-section">
            <h3>会话列表</h3>
            <div class="chat-list" id="chatList">
                <div class="chat-item active" data-chat-id="chat1">会话 1</div>
                <div class="chat-item" data-chat-id="chat2">会话 2</div>
                <div class="chat-item" data-chat-id="chat3">会话 3</div>
            </div>
        </div>

        <div class="test-section">
            <h3>状态面板</h3>
            <div class="status-panel">
                <div class="status-item">
                    <span class="status-label">当前会话:</span>
                    <span class="status-value" id="currentChat">chat1</span>
                </div>
                <div class="status-item">
                    <span class="status-label">流式状态:</span>
                    <span class="status-value" id="streamingStatus">未开始</span>
                </div>
                <div class="status-item">
                    <span class="status-label">流式会话ID:</span>
                    <span class="status-value" id="streamingChatId">无</span>
                </div>
                <div class="status-item">
                    <span class="status-label">思维链状态:</span>
                    <span class="status-value" id="thinkingStatus">未激活</span>
                </div>
                <div class="status-item">
                    <span class="status-label">思维链会话ID:</span>
                    <span class="status-value" id="thinkingChatId">无</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>消息区域</h3>
            <div class="message-area" id="messageArea">
                <div class="message user">用户: 请开始思考并回答问题</div>
                <div class="message assistant" id="assistantMessage">AI: 正在准备回答...</div>
            </div>
        </div>

        <div class="test-section">
            <h3>测试控制</h3>
            <div class="controls">
                <button class="btn" onclick="startStreaming()">开始流式输出</button>
                <button class="btn" onclick="stopStreaming()">停止流式输出</button>
                <button class="btn" onclick="switchToRandomChat()">随机切换会话</button>
                <button class="btn danger" onclick="deleteCurrentChat()">删除当前会话</button>
                <button class="btn" onclick="clearLog()">清空日志</button>
            </div>
        </div>

        <div class="test-section">
            <h3>操作日志</h3>
            <div class="log" id="logArea"></div>
        </div>
    </div>

    <script>
        // 模拟应用状态
        const testState = {
            currentChat: 'chat1',
            isStreaming: false,
            streamingChatId: null,
            streamingThinkingChain: {
                isInThinking: false,
                chatId: null
            },
            streamingInterval: null,
            messageContent: ''
        };

        // 日志函数
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logArea.appendChild(entry);
            logArea.scrollTop = logArea.scrollHeight;
        }

        // 更新状态显示
        function updateStatusDisplay() {
            document.getElementById('currentChat').textContent = testState.currentChat;
            document.getElementById('streamingStatus').textContent = testState.isStreaming ? '进行中' : '未开始';
            document.getElementById('streamingChatId').textContent = testState.streamingChatId || '无';
            document.getElementById('thinkingStatus').textContent = testState.streamingThinkingChain.isInThinking ? '激活' : '未激活';
            document.getElementById('thinkingChatId').textContent = testState.streamingThinkingChain.chatId || '无';
        }

        // 切换会话
        function selectChat(chatId) {
            if (testState.currentChat === chatId) return;
            
            log(`尝试切换到会话: ${chatId}`);
            
            // 检查是否正在流式输出且不是当前会话
            if (testState.isStreaming && testState.streamingChatId !== chatId) {
                log('检测到流式输出冲突，中断当前流式输出', 'warning');
                stopStreaming();
            }
            
            // 更新当前会话
            const oldChat = testState.currentChat;
            testState.currentChat = chatId;
            
            // 重置思维链状态（如果不是当前流式会话）
            if (testState.streamingChatId !== chatId) {
                if (testState.streamingThinkingChain.isInThinking) {
                    log('重置思维链状态', 'info');
                    testState.streamingThinkingChain.isInThinking = false;
                    testState.streamingThinkingChain.chatId = null;
                }
            }
            
            // 更新UI
            document.querySelectorAll('.chat-item').forEach(item => {
                item.classList.toggle('active', item.dataset.chatId === chatId);
            });
            
            updateStatusDisplay();
            log(`成功切换会话: ${oldChat} -> ${chatId}`, 'info');
        }

        // 开始流式输出
        function startStreaming() {
            if (testState.isStreaming) {
                log('流式输出已在进行中', 'warning');
                return;
            }
            
            testState.isStreaming = true;
            testState.streamingChatId = testState.currentChat;
            testState.messageContent = '';
            
            log(`开始流式输出，会话ID: ${testState.streamingChatId}`, 'info');
            
            // 模拟思维链开始
            setTimeout(() => {
                if (testState.isStreaming && testState.streamingChatId === testState.currentChat) {
                    testState.streamingThinkingChain.isInThinking = true;
                    testState.streamingThinkingChain.chatId = testState.streamingChatId;
                    log('思维链开始', 'info');
                    updateStatusDisplay();
                }
            }, 1000);
            
            // 模拟流式输出
            let counter = 0;
            testState.streamingInterval = setInterval(() => {
                if (!testState.isStreaming) return;
                
                // 检查会话隔离
                if (testState.streamingChatId !== testState.currentChat) {
                    log('检测到会话不匹配，停止流式输出', 'error');
                    stopStreaming();
                    return;
                }
                
                counter++;
                testState.messageContent += `流式内容片段 ${counter}... `;
                
                // 更新消息显示
                const assistantMessage = document.getElementById('assistantMessage');
                assistantMessage.textContent = `AI (${testState.streamingChatId}): ${testState.messageContent}`;
                
                if (counter >= 10) {
                    log('流式输出完成', 'info');
                    stopStreaming();
                }
            }, 500);
            
            updateStatusDisplay();
        }

        // 停止流式输出
        function stopStreaming() {
            if (!testState.isStreaming) return;
            
            testState.isStreaming = false;
            testState.streamingChatId = null;
            
            if (testState.streamingInterval) {
                clearInterval(testState.streamingInterval);
                testState.streamingInterval = null;
            }
            
            // 重置思维链状态
            testState.streamingThinkingChain.isInThinking = false;
            testState.streamingThinkingChain.chatId = null;
            
            log('流式输出已停止', 'info');
            updateStatusDisplay();
        }

        // 随机切换会话
        function switchToRandomChat() {
            const chats = ['chat1', 'chat2', 'chat3'];
            const otherChats = chats.filter(id => id !== testState.currentChat);
            const randomChat = otherChats[Math.floor(Math.random() * otherChats.length)];
            selectChat(randomChat);
        }

        // 删除当前会话
        function deleteCurrentChat() {
            const chatToDelete = testState.currentChat;
            
            // 如果正在删除的是流式输出的会话，中断流式输出
            if (testState.isStreaming && testState.streamingChatId === chatToDelete) {
                log('删除流式输出会话，中断流式输出', 'warning');
                stopStreaming();
            }
            
            log(`模拟删除会话: ${chatToDelete}`, 'warning');
            
            // 切换到其他会话
            const chats = ['chat1', 'chat2', 'chat3'];
            const otherChats = chats.filter(id => id !== chatToDelete);
            if (otherChats.length > 0) {
                selectChat(otherChats[0]);
            }
        }

        // 清空日志
        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
        }

        // 初始化事件监听
        document.addEventListener('DOMContentLoaded', () => {
            // 会话切换事件
            document.querySelectorAll('.chat-item').forEach(item => {
                item.addEventListener('click', () => {
                    selectChat(item.dataset.chatId);
                });
            });
            
            updateStatusDisplay();
            log('测试页面初始化完成', 'info');
        });
    </script>
</body>
</html>
