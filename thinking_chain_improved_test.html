<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>改进的思维链渲染测试</title>
    <style>
        :root {
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --bg-hover: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #475569;
            --text-tertiary: #64748b;
            --border-primary: #e2e8f0;
            --border-secondary: #cbd5e1;
            --accent-primary: #3b82f6;
            --accent-light: #dbeafe;
            --radius-sm: 4px;
            --radius-md: 6px;
            --radius-lg: 8px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
            margin: 0;
            padding: 20px;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #1e293b;
            margin-bottom: 2rem;
            text-align: center;
        }

        .test-section {
            margin-bottom: 3rem;
        }

        .test-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #374151;
        }

        .message-container {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 1rem;
            background: #f9fafb;
        }

        .test-controls {
            margin-bottom: 1rem;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            background: #3b82f6;
            color: white;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s ease;
        }

        .btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6b7280;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        /* 思维链样式 - 改进版 */
        .thinking-chain {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, rgba(147, 51, 234, 0.03) 100%);
            border: 1px solid rgba(59, 130, 246, 0.15);
            border-radius: 12px;
            margin: 1.5rem 0;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            position: relative;
        }

        .thinking-chain::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, rgba(59, 130, 246, 0.6) 0%, rgba(147, 51, 234, 0.6) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .thinking-chain:hover::before {
            opacity: 1;
        }

        .thinking-chain-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 1.5rem;
            cursor: pointer;
            background: rgba(255, 255, 255, 0.5);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(59, 130, 246, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            user-select: none;
            position: relative;
        }

        .thinking-chain-header:hover {
            background: rgba(59, 130, 246, 0.08);
            transform: translateY(-1px);
        }

        .thinking-chain-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 0.9rem;
            font-weight: 600;
            color: #374151;
            letter-spacing: -0.01em;
        }

        .thinking-chain-icon {
            width: 18px;
            height: 18px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            color: #3b82f6;
            filter: drop-shadow(0 1px 2px rgba(59, 130, 246, 0.2));
        }

        .thinking-chain-header.expanded .thinking-chain-icon {
            transform: rotate(90deg) scale(1.1);
            color: #6366f1;
        }

        .thinking-chain-content {
            max-height: 0;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.3);
        }

        .thinking-chain-content.expanded {
            max-height: 500px;
            overflow-y: auto;
            border-top: 1px solid rgba(59, 130, 246, 0.1);
        }

        .thinking-chain-inner {
            padding: 1.5rem;
            color: #4b5563;
            font-size: 0.9rem;
            line-height: 1.7;
            white-space: pre-wrap;
            word-wrap: break-word;
            overflow-wrap: break-word;
            font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(249, 250, 251, 0.8) 100%);
            border-radius: 0 0 12px 12px;
        }

        .thinking-chain-badge {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
            color: #3b82f6;
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            border: 1px solid rgba(59, 130, 246, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            letter-spacing: -0.01em;
        }

        .thinking-chain-badge:hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(147, 51, 234, 0.15) 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
        }

        /* 思考中状态 */
        .thinking-chain.thinking {
            border-color: rgba(59, 130, 246, 0.4);
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(147, 51, 234, 0.08) 100%);
            animation: thinking-pulse 2.5s ease-in-out infinite;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.15);
        }

        .thinking-chain.thinking::before {
            opacity: 1;
            animation: thinking-gradient 3s ease-in-out infinite;
        }

        .thinking-chain.thinking .thinking-chain-header {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.12) 0%, rgba(147, 51, 234, 0.12) 100%);
            backdrop-filter: blur(15px);
        }

        .thinking-chain.thinking .thinking-chain-title {
            color: #3b82f6;
        }

        .thinking-chain.thinking .thinking-chain-badge {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(147, 51, 234, 0.2) 100%);
            color: #1e40af;
            animation: thinking-badge-pulse 2s ease-in-out infinite;
            border-color: rgba(59, 130, 246, 0.3);
        }

        /* 完成状态 */
        .thinking-chain.completed {
            border-color: rgba(34, 197, 94, 0.3);
        }

        .thinking-chain.completed::before {
            background: linear-gradient(90deg, rgba(34, 197, 94, 0.6) 0%, rgba(59, 130, 246, 0.6) 100%);
            opacity: 0.8;
        }

        .thinking-chain.completed .thinking-chain-badge {
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
            color: #059669;
            border-color: rgba(34, 197, 94, 0.2);
        }

        /* 动画 */
        @keyframes thinking-pulse {
            0%, 100% {
                box-shadow: 0 4px 20px rgba(59, 130, 246, 0.15);
                transform: translateY(0);
            }
            50% {
                box-shadow: 0 8px 30px rgba(59, 130, 246, 0.25);
                transform: translateY(-2px);
            }
        }

        @keyframes thinking-gradient {
            0%, 100% {
                background: linear-gradient(90deg, rgba(59, 130, 246, 0.6) 0%, rgba(147, 51, 234, 0.6) 100%);
            }
            50% {
                background: linear-gradient(90deg, rgba(147, 51, 234, 0.6) 0%, rgba(59, 130, 246, 0.6) 100%);
            }
        }

        @keyframes thinking-badge-pulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.8;
                transform: scale(1.05);
            }
        }

        /* 打字效果 */
        .thinking-chain-inner.typing::after {
            content: '▋';
            animation: typing-cursor 1.2s infinite;
            color: #3b82f6;
            font-weight: bold;
            margin-left: 2px;
        }

        @keyframes typing-cursor {
            0%, 50% {
                opacity: 1;
                transform: scale(1);
            }
            51%, 100% {
                opacity: 0.3;
                transform: scale(0.9);
            }
        }

        /* 滚动条样式 */
        .thinking-chain-content::-webkit-scrollbar {
            width: 8px;
        }

        .thinking-chain-content::-webkit-scrollbar-track {
            background: rgba(59, 130, 246, 0.05);
            border-radius: 4px;
            margin: 4px 0;
        }

        .thinking-chain-content::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.4) 0%, rgba(147, 51, 234, 0.4) 100%);
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .thinking-chain-content::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.6) 0%, rgba(147, 51, 234, 0.6) 100%);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>改进的思维链渲染效果测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. 静态思维链展示</div>
            <div class="test-controls">
                <button class="btn" onclick="toggleThinkingChain('static-1')">切换展开/折叠</button>
                <button class="btn btn-secondary" onclick="addCompletedState('static-1')">添加完成状态</button>
            </div>
            
            <div class="thinking-chain completed" data-thinking-id="static-1">
                <div class="thinking-chain-header">
                    <div class="thinking-chain-title">
                        <svg class="thinking-chain-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                        <span>思维过程</span>
                        <span class="thinking-chain-badge">展开查看</span>
                    </div>
                </div>
                <div class="thinking-chain-content" id="static-1-content">
                    <div class="thinking-chain-inner">这是一个静态的思维链示例。

让我分析一下这个问题：

1. 首先需要理解用户的需求
2. 然后考虑可能的解决方案
3. 最后选择最优的实现方式

经过思考，我认为应该采用渐进式的方法来解决这个问题。</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">2. 思考中状态演示</div>
            <div class="test-controls">
                <button class="btn" onclick="simulateThinking()">模拟思考过程</button>
                <button class="btn btn-secondary" onclick="stopThinking()">停止思考</button>
            </div>
            
            <div class="thinking-chain thinking" data-thinking-id="thinking-demo">
                <div class="thinking-chain-header">
                    <div class="thinking-chain-title">
                        <svg class="thinking-chain-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                        <span>思维过程</span>
                        <span class="thinking-chain-badge">思考中...</span>
                    </div>
                </div>
                <div class="thinking-chain-content expanded">
                    <div class="thinking-chain-inner typing" id="thinking-content">正在思考问题的解决方案...</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 自动滚动测试</div>
            <div class="test-controls">
                <button class="btn" onclick="testAutoScroll()">测试自动滚动</button>
                <button class="btn btn-secondary" onclick="clearScrollTest()">清空内容</button>
            </div>
            
            <div class="message-container" id="scroll-test-container">
                <p>这里将测试自动滚动功能...</p>
            </div>
        </div>
    </div>

    <script>
        // 切换思维链展开/折叠状态
        function toggleThinkingChain(id) {
            const header = document.querySelector(`[data-thinking-id="${id}"] .thinking-chain-header`);
            const content = document.getElementById(`${id}-content`);
            const badge = header.querySelector('.thinking-chain-badge');
            const thinkingChain = header.closest('.thinking-chain');

            if (!header || !content || !badge) return;

            const isExpanded = header.classList.contains('expanded');

            if (isExpanded) {
                // 折叠
                header.classList.remove('expanded');
                content.classList.remove('expanded');
                badge.textContent = '展开查看';
            } else {
                // 展开
                header.classList.add('expanded');
                content.classList.add('expanded');
                badge.textContent = '收起';
                
                // 展开后自动滚动到思维链位置
                setTimeout(() => {
                    thinkingChain.scrollIntoView({ 
                        behavior: 'smooth', 
                        block: 'start' 
                    });
                }, 300);
            }
        }

        // 添加完成状态
        function addCompletedState(id) {
            const thinkingChain = document.querySelector(`[data-thinking-id="${id}"]`);
            if (thinkingChain) {
                thinkingChain.classList.add('completed');
            }
        }

        // 模拟思考过程
        function simulateThinking() {
            const thinkingContent = document.getElementById('thinking-content');
            const thinkingChain = document.querySelector('[data-thinking-id="thinking-demo"]');
            const badge = thinkingChain.querySelector('.thinking-chain-badge');
            
            thinkingChain.classList.add('thinking');
            thinkingChain.classList.remove('completed');
            thinkingContent.classList.add('typing');
            badge.textContent = '思考中...';
            
            const thoughts = [
                '正在思考问题的解决方案...',
                '分析用户需求...',
                '考虑技术实现方案...',
                '评估不同方案的优缺点...',
                '选择最优解决方案...',
                '思考完成！'
            ];
            
            let index = 0;
            const interval = setInterval(() => {
                if (index < thoughts.length) {
                    thinkingContent.textContent = thoughts[index];
                    index++;
                } else {
                    clearInterval(interval);
                    stopThinking();
                }
            }, 1000);
        }

        // 停止思考
        function stopThinking() {
            const thinkingContent = document.getElementById('thinking-content');
            const thinkingChain = document.querySelector('[data-thinking-id="thinking-demo"]');
            const badge = thinkingChain.querySelector('.thinking-chain-badge');
            
            thinkingChain.classList.remove('thinking');
            thinkingChain.classList.add('completed');
            thinkingContent.classList.remove('typing');
            badge.textContent = '展开查看';
        }

        // 测试自动滚动
        function testAutoScroll() {
            const container = document.getElementById('scroll-test-container');
            
            for (let i = 1; i <= 20; i++) {
                setTimeout(() => {
                    const p = document.createElement('p');
                    p.textContent = `这是第 ${i} 条消息，用于测试自动滚动功能。`;
                    container.appendChild(p);
                    
                    // 自动滚动到底部
                    container.scrollTo({
                        top: container.scrollHeight,
                        behavior: 'smooth'
                    });
                }, i * 200);
            }
        }

        // 清空滚动测试内容
        function clearScrollTest() {
            const container = document.getElementById('scroll-test-container');
            container.innerHTML = '<p>这里将测试自动滚动功能...</p>';
        }
    </script>
</body>
</html>
