<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>思维链最终测试</title>
    <style>
        :root {
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: #e9ecef;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --text-tertiary: #adb5bd;
            --border-primary: #dee2e6;
            --border-secondary: #e9ecef;
            --accent-primary: #007bff;
            --accent-light: #e3f2fd;
            --radius-sm: 4px;
            --radius-lg: 8px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: var(--bg-primary);
            color: var(--text-primary);
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        h1 {
            color: var(--text-primary);
            margin-bottom: 30px;
        }

        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
        }

        /* 思维链样式 - 修复后的版本 */
        .thinking-chain {
            background: var(--bg-secondary);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-lg);
            margin: 1rem 0;
            overflow: hidden;
        }

        .thinking-chain-header {
            padding: 1rem 1.25rem;
            cursor: pointer;
            user-select: none;
            transition: background-color 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .thinking-chain-header:hover {
            background: var(--bg-tertiary);
        }

        .thinking-chain-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .thinking-chain-icon {
            width: 1rem;
            height: 1rem;
            transition: transform 0.2s ease;
            color: var(--text-tertiary);
        }

        .thinking-chain-header.expanded .thinking-chain-icon {
            transform: rotate(90deg);
        }

        .thinking-chain-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .thinking-chain-content.expanded {
            max-height: 400px; /* 设置合理的最大高度 */
            overflow-y: auto; /* 添加垂直滚动条 */
        }

        .thinking-chain-inner {
            padding: 1.25rem;
            color: var(--text-tertiary);
            font-size: 0.875rem;
            line-height: 1.6;
            white-space: pre-wrap;
            word-wrap: break-word;
            overflow-wrap: break-word;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
        }

        /* 思维链滚动条样式 */
        .thinking-chain-content::-webkit-scrollbar {
            width: 6px;
        }

        .thinking-chain-content::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
            border-radius: 3px;
        }

        .thinking-chain-content::-webkit-scrollbar-thumb {
            background: var(--border-secondary);
            border-radius: 3px;
        }

        .thinking-chain-content::-webkit-scrollbar-thumb:hover {
            background: var(--text-tertiary);
        }

        .thinking-chain-badge {
            background: var(--accent-light);
            color: var(--accent-primary);
            padding: 0.25rem 0.5rem;
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 500;
        }

        .test-button {
            background: var(--accent-primary);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: var(--radius-sm);
            cursor: pointer;
            margin: 10px 5px;
        }

        .test-button:hover {
            opacity: 0.9;
        }

        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>思维链修复效果验证</h1>
        
        <div class="test-section">
            <h2>测试1: 换行符处理</h2>
            <p>测试换行符是否正确转换为HTML换行，而不是显示&lt;br&gt;标签</p>
            <div id="newline-test"></div>
            <div id="newline-status"></div>
            <button class="test-button" onclick="testNewlines()">测试换行符</button>
        </div>

        <div class="test-section">
            <h2>测试2: HTML标签转义</h2>
            <p>测试危险的HTML标签是否被正确转义</p>
            <div id="html-test"></div>
            <div id="html-status"></div>
            <button class="test-button" onclick="testHtmlEscape()">测试HTML转义</button>
        </div>

        <div class="test-section">
            <h2>测试3: 长内容滚动</h2>
            <p>测试长内容是否能正确滚动</p>
            <div id="scroll-test"></div>
            <div id="scroll-status"></div>
            <button class="test-button" onclick="testScrolling()">测试滚动功能</button>
        </div>

        <div class="test-section">
            <h2>测试4: 综合测试</h2>
            <p>包含换行、HTML标签、长内容的综合测试</p>
            <div id="comprehensive-test"></div>
            <div id="comprehensive-status"></div>
            <button class="test-button" onclick="testComprehensive()">综合测试</button>
        </div>
    </div>

    <script>
        // 修复后的processThinkingContent函数
        function processThinkingContent(content) {
            if (!content) return '';
            // 直接进行字符替换，避免过度转义
            const escaped = content
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#39;')
                .replace(/\n/g, '<br>');
            return escaped;
        }

        // 创建思维链HTML
        function createThinkingChainHtml(content, id) {
            const uniqueId = `thinking-chain-${Date.now()}-${id}`;
            const processedContent = processThinkingContent(content);
            return `
                <div class="thinking-chain" data-thinking-id="${uniqueId}">
                    <div class="thinking-chain-header">
                        <div class="thinking-chain-title">
                            <svg class="thinking-chain-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                            <span>思维过程</span>
                            <span class="thinking-chain-badge">展开查看</span>
                        </div>
                    </div>
                    <div class="thinking-chain-content" id="${uniqueId}-content">
                        <div class="thinking-chain-inner">${processedContent}</div>
                    </div>
                </div>
            `;
        }

        // 切换思维链展开/折叠状态
        function toggleThinkingChain(id) {
            const header = document.querySelector(`[data-thinking-id="${id}"] .thinking-chain-header`);
            const content = document.getElementById(`${id}-content`);
            const badge = header.querySelector('.thinking-chain-badge');

            if (header.classList.contains('expanded')) {
                header.classList.remove('expanded');
                content.classList.remove('expanded');
                badge.textContent = '展开查看';
            } else {
                header.classList.add('expanded');
                content.classList.add('expanded');
                badge.textContent = '收起';
            }
        }

        // 绑定点击事件
        function bindThinkingChainEvents(container) {
            const header = container.querySelector('.thinking-chain-header');
            const thinkingId = header.closest('.thinking-chain').getAttribute('data-thinking-id');
            header.onclick = () => toggleThinkingChain(thinkingId);
        }

        // 验证结果
        function validateResult(container, expectedFeatures, statusElementId) {
            const innerHTML = container.innerHTML;
            const statusElement = document.getElementById(statusElementId);
            let allPassed = true;
            let messages = [];

            expectedFeatures.forEach(feature => {
                if (feature.check(innerHTML)) {
                    messages.push(`✓ ${feature.name}`);
                } else {
                    messages.push(`✗ ${feature.name}`);
                    allPassed = false;
                }
            });

            statusElement.className = `status ${allPassed ? 'success' : 'error'}`;
            statusElement.innerHTML = messages.join('<br>');
        }

        // 测试换行符
        function testNewlines() {
            const content = `第一行内容
第二行内容
第三行内容`;
            const html = createThinkingChainHtml(content, 'newline');
            const container = document.getElementById('newline-test');
            container.innerHTML = html;
            bindThinkingChainEvents(container);

            validateResult(container, [
                {
                    name: '换行符转换为<br>标签',
                    check: (html) => html.includes('<br>') && !html.includes('&lt;br&gt;')
                },
                {
                    name: '不显示原始换行符',
                    check: (html) => !html.includes('\n')
                }
            ], 'newline-status');
        }

        // 测试HTML转义
        function testHtmlEscape() {
            const content = `危险内容：<script>alert('xss')</script>
其他标签：<div>test</div>
特殊字符：& < > " '`;
            const html = createThinkingChainHtml(content, 'html');
            const container = document.getElementById('html-test');
            container.innerHTML = html;
            bindThinkingChainEvents(container);

            validateResult(container, [
                {
                    name: 'script标签被转义',
                    check: (html) => html.includes('&lt;script&gt;') && !html.includes('<script>')
                },
                {
                    name: 'div标签被转义',
                    check: (html) => html.includes('&lt;div&gt;') && !html.includes('<div>test</div>')
                },
                {
                    name: '特殊字符被转义',
                    check: (html) => html.includes('&amp;') && html.includes('&lt;') && html.includes('&gt;')
                }
            ], 'html-status');
        }

        // 测试滚动功能
        function testScrolling() {
            const content = Array.from({length: 50}, (_, i) => `这是第${i + 1}行内容，用于测试滚动功能。`).join('\n');
            const html = createThinkingChainHtml(content, 'scroll');
            const container = document.getElementById('scroll-test');
            container.innerHTML = html;
            bindThinkingChainEvents(container);

            // 自动展开以测试滚动
            setTimeout(() => {
                const header = container.querySelector('.thinking-chain-header');
                const thinkingId = header.closest('.thinking-chain').getAttribute('data-thinking-id');
                toggleThinkingChain(thinkingId);

                setTimeout(() => {
                    const contentElement = container.querySelector('.thinking-chain-content');
                    validateResult(container, [
                        {
                            name: '内容区域有滚动条',
                            check: () => contentElement.scrollHeight > contentElement.clientHeight
                        },
                        {
                            name: '最大高度限制生效',
                            check: () => contentElement.clientHeight <= 400
                        }
                    ], 'scroll-status');
                }, 500);
            }, 100);
        }

        // 综合测试
        function testComprehensive() {
            const content = `综合测试内容：

第一部分：换行测试
这是第一行
这是第二行
这是第三行

第二部分：HTML转义测试
危险脚本：<script>alert('test')</script>
样式标签：<style>body{color:red}</style>
普通标签：<div><p>测试内容</p></div>

第三部分：特殊字符测试
& 符号测试
< 小于号测试
> 大于号测试
" 双引号测试
' 单引号测试

第四部分：长内容测试
${Array.from({length: 20}, (_, i) => `长内容第${i + 1}行`).join('\n')}`;

            const html = createThinkingChainHtml(content, 'comprehensive');
            const container = document.getElementById('comprehensive-test');
            container.innerHTML = html;
            bindThinkingChainEvents(container);

            validateResult(container, [
                {
                    name: '换行符正确处理',
                    check: (html) => html.includes('<br>') && !html.includes('&lt;br&gt;')
                },
                {
                    name: 'HTML标签被转义',
                    check: (html) => html.includes('&lt;script&gt;') && !html.includes('<script>')
                },
                {
                    name: '特殊字符被转义',
                    check: (html) => html.includes('&amp;') && html.includes('&lt;') && html.includes('&gt;')
                },
                {
                    name: '思维链结构完整',
                    check: (html) => html.includes('thinking-chain') && html.includes('thinking-chain-header')
                }
            ], 'comprehensive-status');
        }

        // 页面加载完成后自动运行所有测试
        window.onload = function() {
            setTimeout(() => {
                testNewlines();
                setTimeout(() => testHtmlEscape(), 200);
                setTimeout(() => testScrolling(), 400);
                setTimeout(() => testComprehensive(), 600);
            }, 100);
        };
    </script>
</body>
</html>
