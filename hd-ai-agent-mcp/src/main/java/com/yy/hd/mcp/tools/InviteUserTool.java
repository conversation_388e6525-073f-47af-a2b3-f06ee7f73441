package com.yy.hd.mcp.tools;

import com.yy.hd.commons.uri.HttpUris;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Slf4j
@AllArgsConstructor
@Component
public class InviteUserTool {

    private final WebClient defaultWebClient;

    private static final String QUERY_URL = HttpUris.QUERY_INVITE_USER_URI;

    @Tool(name = "InviteUserTool", description = "邀好友得限定王者皮肤活动")
    public Mono<String> inviteUserTool(@ToolParam(description = "uid，邀请人", required = false) String uid,
                                       @ToolParam(description = "invitedUid，被邀请人", required = false) String invitedUid,
                                       @ToolParam(description = "查询条数，默认30条", required = false) Integer limit) {
        log.info("inviteUserTool req, uid:{}, invitedUid:{}, limit:{}", uid, invitedUid, limit);
        if (StringUtils.isBlank(uid) && StringUtils.isBlank(invitedUid)) {
            return Mono.just("请提供uid或者invitedUid");
        }
        if (limit == null || limit <= 0) {
            limit = 30;
        }
        StringBuilder queryString = new StringBuilder("?");
        if (StringUtils.isNotBlank(uid)) {
            queryString.append("uid=").append(uid);
        }
        if (StringUtils.isNotBlank(invitedUid)) {
            if (!queryString.isEmpty()) {
                queryString.append("&");
            }
            queryString.append("invitedUid=").append(invitedUid);
        }
        queryString.append("&limit=").append(limit);
        return defaultWebClient.get()
                .uri(QUERY_URL + queryString)
                .retrieve()
                .bodyToMono(String.class)
                .doOnNext(response -> log.info("inviteUserTool rsp:{}", response))
                .onErrorResume(e -> {
                    log.error("inviteUserTool fail", e);
                    return Mono.just("查询邀好友得限定王者皮肤活动数据失败");
                });
    }

}
