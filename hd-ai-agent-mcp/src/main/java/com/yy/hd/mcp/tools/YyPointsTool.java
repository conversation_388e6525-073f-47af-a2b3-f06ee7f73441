package com.yy.hd.mcp.tools;

import com.yy.hd.commons.uri.HttpUris;
import com.yy.hd.mcp.service.UserInfoService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

@Slf4j
@AllArgsConstructor
@Component
public class YyPointsTool {

    private final WebClient defaultWebClient;

    private final UserInfoService userInfoService;

    private static final String QUERY_URL = HttpUris.QUERY_YY_POINTS_URI;

    @Tool(name = "YyPointsTool", description = "用户的在线时长")
    public Mono<String> yyPointsTool(@ToolParam(description = "用户yy", required = false) String yy,
                                     @ToolParam(description = "用户uid", required = false) String uid,
                                     @ToolParam(description = "日期格式：yyyyMMdd", required = false) String queryDate) {
        log.info("yyPointsTool, yy:{}, uid:{}, queryDate:{}", yy, uid, queryDate);
        if (StringUtils.isBlank(yy) && StringUtils.isBlank(uid)) {
            return Mono.just("请输入用户yy号或uid");
        }

        return Mono.defer(() -> getUid(yy, uid))
                .flatMap(yyUid -> {
                    //默认一天前
                    String date = Optional.ofNullable(queryDate)
                            .orElse(LocalDate.now().minusDays(1)
                                    .format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                    try {
                        return defaultWebClient.get()
                                .uri(QUERY_URL, yyUid, date)
                                .retrieve()
                                .bodyToMono(String.class);
                    } catch (Exception e) {
                        log.error("yyPointsTool fail", e);
                        return Mono.just("查询用户在线时长失败");
                    }
                });

    }

    private Mono<Long> getUid(String yy, String uid) {
        if (StringUtils.isNotBlank(yy)) {
            return userInfoService.getUidByYY(yy)
                    .switchIfEmpty(Mono.just(0L));
        } else if (StringUtils.isNotBlank(uid)) {
            return Mono.just(Long.parseLong(uid))
                    .onErrorResume(NumberFormatException.class, e -> Mono.just(0L));
        }
        return Mono.just(0L);
    }
}
