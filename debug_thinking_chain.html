<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>思维链问题调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        .html-result {
            background: #e8f5e8;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            border: 1px solid #4CAF50;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>思维链HTML标签问题调试</h1>
    
    <div class="test-section">
        <h2>测试输入内容</h2>
        <div class="result" id="input-content"></div>
    </div>

    <div class="test-section">
        <h2>当前的processThinkingContent函数处理结果</h2>
        <div class="result" id="current-result"></div>
        <div class="html-result" id="current-html"></div>
        <button onclick="testCurrent()">测试当前方法</button>
    </div>

    <div class="test-section">
        <h2>修复后的processThinkingContent函数处理结果</h2>
        <div class="result" id="fixed-result"></div>
        <div class="html-result" id="fixed-html"></div>
        <button onclick="testFixed()">测试修复方法</button>
    </div>

    <div class="test-section">
        <h2>对比分析</h2>
        <div id="analysis"></div>
        <button onclick="runAnalysis()">运行分析</button>
    </div>

    <script>
        // 测试内容
        const testContent = `这是第一行内容
这是第二行内容
包含HTML标签：<script>alert('test')</script>
还有其他标签：<div>test</div>
特殊字符：& < > " '`;

        // 显示输入内容
        document.getElementById('input-content').textContent = testContent;

        // 当前的HTML转义函数
        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 当前的processThinkingContent函数
        function processThinkingContentCurrent(content) {
            if (!content) return '';
            // 先转义HTML标签，但保留换行符
            const escaped = escapeHtml(content);
            // 将换行符转换为<br>标签，确保正确显示
            const withBreaks = escaped.replace(/\n/g, '<br>');
            return withBreaks;
        }

        // 修复后的processThinkingContent函数
        function processThinkingContentFixed(content) {
            if (!content) return '';
            // 先转义HTML标签
            const escaped = escapeHtml(content);
            // 将换行符转换为<br>标签
            const withBreaks = escaped.replace(/\n/g, '<br>');
            return withBreaks;
        }

        // 更好的修复方案
        function processThinkingContentBetter(content) {
            if (!content) return '';
            // 直接处理换行符，不使用innerHTML来避免HTML标签被转义后又被处理
            const escaped = content
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#39;')
                .replace(/\n/g, '<br>');
            return escaped;
        }

        function testCurrent() {
            const result = processThinkingContentCurrent(testContent);
            document.getElementById('current-result').textContent = result;
            document.getElementById('current-html').innerHTML = result;
        }

        function testFixed() {
            const result = processThinkingContentBetter(testContent);
            document.getElementById('fixed-result').textContent = result;
            document.getElementById('fixed-html').innerHTML = result;
        }

        function runAnalysis() {
            const currentResult = processThinkingContentCurrent(testContent);
            const fixedResult = processThinkingContentBetter(testContent);
            
            const analysis = `
分析结果：

1. 当前方法问题：
   - escapeHtml函数将所有内容转义，包括我们想要的<br>标签
   - 结果：&lt;br&gt; 而不是 <br>
   - 换行符无法正确显示为HTML换行

2. 修复方法优势：
   - 直接进行字符替换，避免过度转义
   - <br>标签不会被转义
   - 换行符正确转换为HTML换行
   - 仍然安全地转义危险的HTML标签

3. 具体差异：
   当前方法输出长度: ${currentResult.length}
   修复方法输出长度: ${fixedResult.length}
   
   当前方法包含 "&lt;br&gt;": ${currentResult.includes('&lt;br&gt;')}
   修复方法包含 "<br>": ${fixedResult.includes('<br>')}
            `;
            
            document.getElementById('analysis').innerHTML = `<pre>${analysis}</pre>`;
        }

        // 自动运行测试
        window.onload = function() {
            testCurrent();
            testFixed();
            runAnalysis();
        };
    </script>
</body>
</html>
