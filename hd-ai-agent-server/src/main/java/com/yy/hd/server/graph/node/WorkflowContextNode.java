package com.yy.hd.server.graph.node;

import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import com.yy.hd.model.ChatClientProvider;
import com.yy.hd.model.properties.ModelProperties;
import com.yy.hd.model.properties.ModelProviderProperties;
import com.yy.hd.server.graph.SourceType;
import com.yy.hd.server.graph.WorkflowContext;
import com.yy.hd.server.graph.WorkflowRegisterKeys;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.alibaba.cloud.ai.graph.StateGraph.END;

/**
 * 召回/重排上下文设置
 */
@Component
public class WorkflowContextNode implements NodeAction {

    @Resource
    private ChatClientProvider chatClientProvider;

    @Override
    public Map<String, Object> apply(OverAllState state) throws Exception {
        WorkflowContext workflowContext = (WorkflowContext) state.value(WorkflowRegisterKeys.WORKFLOW_CONTEXT_KEY.getKey())
                .orElse(null);
        SourceType sourceType = SourceType.fromType(workflowContext.getSource());
        if (sourceType == SourceType.UNKNOWN) {
            workflowContext.setNextNodeId(END);
            return Map.of(WorkflowRegisterKeys.MESSAGES_KEY.getKey(), "未知的调用来源！");
        }
        Map<String, ModelProviderProperties> modelProviders = chatClientProvider.getModelProviders();
        ModelProviderProperties modelProviderProperties = modelProviders.get(workflowContext.getProvider());
        if (modelProviderProperties == null) {
            workflowContext.setNextNodeId(END);
            return Map.of(WorkflowRegisterKeys.MESSAGES_KEY.getKey(), "未知的模型提供商！");
        }
        ModelProperties modelProperties = modelProviderProperties.getModels().stream()
                .filter(model -> model.getModelName().equals(workflowContext.getModel()))
                .findFirst()
                .orElse(null);
        if (modelProperties == null) {
            workflowContext.setNextNodeId(END);
            return Map.of(WorkflowRegisterKeys.MESSAGES_KEY.getKey(), "未知的模型！");
        }
        workflowContext.setNextNodeId("rankingContextNode");
        return Map.of(WorkflowRegisterKeys.WORKFLOW_CONTEXT_KEY.getKey(), workflowContext);
    }

}
